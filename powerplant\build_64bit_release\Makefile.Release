#############################################################################
# Makefile for building: SmartBurning
# Generated by qmake (3.1) (Qt 6.9.1)
# Project:  ..\SmartBurning.pro
# Template: app
#############################################################################

MAKEFILE      = Makefile.Release

EQ            = =

####### Compiler, tools and options

CC            = gcc
CXX           = g++
DEFINES       = -DUNICODE -D_UNICODE -DWIN32 -DMINGW_HAS_SECURE_API=1 -DENABLE_HARDWARE_DATA -DQT_NO_DEBUG_OUTPUT -DWINVER=0x0601 -D_WIN32_WINNT=0x0601 -DWIN32_LEAN_AND_MEAN -DQT_NO_WIN_ACTIVEQT -DQT_NO_DEBUG -DQT_CHARTS_LIB -DQT_OPENGLWIDGETS_LIB -DQT_MULTIMEDIAWIDGETS_LIB -DQT_WIDGETS_LIB -DQT_QUICKCONTROLS2_LIB -DQT_QUICK_LIB -DQT_OPENGL_LIB -DQT_MULTIMEDIA_LIB -DQT_GUI_LIB -DQT_QMLMETA_LIB -DQT_QMLMODELS_LIB -DQT_QMLWORKERSCRIPT_LIB -DQT_QML_LIB -DQT_QMLINTEGRATION_LIB -DQT_NETWORK_LIB -DQT_CORE_LIB -DQT_NEEDS_QMAIN
CFLAGS        = -fno-keep-inline-dllexport -D_WIN32_WINNT=0x0601 -O2 -Wall -Wextra -Wextra $(DEFINES)
CXXFLAGS      = -fno-keep-inline-dllexport -D_WIN32_WINNT=0x0601 -DWINVER=0x0601 -D_WIN32_WINNT=0x0601 -O2 -std=gnu++1z -Wall -Wextra -Wextra -fexceptions -mthreads $(DEFINES)
INCPATH       = -I../../powerplant -I. -ID:/Development/Qt/6.9.1/mingw_64/include -ID:/Development/Qt/6.9.1/mingw_64/include/QtCharts -ID:/Development/Qt/6.9.1/mingw_64/include/QtOpenGLWidgets -ID:/Development/Qt/6.9.1/mingw_64/include/QtMultimediaWidgets -ID:/Development/Qt/6.9.1/mingw_64/include/QtWidgets -ID:/Development/Qt/6.9.1/mingw_64/include/QtQuickControls2 -ID:/Development/Qt/6.9.1/mingw_64/include/QtQuick -ID:/Development/Qt/6.9.1/mingw_64/include/QtOpenGL -ID:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia -ID:/Development/Qt/6.9.1/mingw_64/include/QtGui -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlMeta -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlModels -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlWorkerScript -ID:/Development/Qt/6.9.1/mingw_64/include/QtQml -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlIntegration -ID:/Development/Qt/6.9.1/mingw_64/include/QtNetwork -ID:/Development/Qt/6.9.1/mingw_64/include/QtCore -Irelease -I. -I/include -ID:/Development/Qt/6.9.1/mingw_64/mkspecs/win32-g++ 
LINKER      =        g++
LFLAGS        =        -Wl,--major-subsystem-version,6 -Wl,--minor-subsystem-version,1 -Wl,-s -Wl,-subsystem,windows -mthreads
LIBS        =        -lws2_32 D:\Development\Qt\6.9.1\mingw_64\lib\libQt6Charts.a D:\Development\Qt\6.9.1\mingw_64\lib\libQt6OpenGLWidgets.a D:\Development\Qt\6.9.1\mingw_64\lib\libQt6MultimediaWidgets.a D:\Development\Qt\6.9.1\mingw_64\lib\libQt6Widgets.a D:\Development\Qt\6.9.1\mingw_64\lib\libQt6QuickControls2.a D:\Development\Qt\6.9.1\mingw_64\lib\libQt6Quick.a D:\Development\Qt\6.9.1\mingw_64\lib\libQt6OpenGL.a D:\Development\Qt\6.9.1\mingw_64\lib\libQt6Multimedia.a D:\Development\Qt\6.9.1\mingw_64\lib\libQt6Gui.a D:\Development\Qt\6.9.1\mingw_64\lib\libQt6QmlMeta.a D:\Development\Qt\6.9.1\mingw_64\lib\libQt6QmlModels.a D:\Development\Qt\6.9.1\mingw_64\lib\libQt6QmlWorkerScript.a D:\Development\Qt\6.9.1\mingw_64\lib\libQt6Qml.a D:\Development\Qt\6.9.1\mingw_64\lib\libQt6Network.a D:\Development\Qt\6.9.1\mingw_64\lib\libQt6Core.a -lmingw32 D:\Development\Qt\6.9.1\mingw_64\lib\libQt6EntryPoint.a -lshell32  
QMAKE         = D:\Development\Qt\6.9.1\mingw_64\bin\qmake.exe
DEL_FILE      = del
CHK_DIR_EXISTS= if not exist
MKDIR         = mkdir
COPY          = copy /y
COPY_FILE     = copy /y
COPY_DIR      = xcopy /s /q /y /i
INSTALL_FILE  = copy /y
INSTALL_PROGRAM = copy /y
INSTALL_DIR   = xcopy /s /q /y /i
QINSTALL      = D:\Development\Qt\6.9.1\mingw_64\bin\qmake.exe -install qinstall
QINSTALL_PROGRAM = D:\Development\Qt\6.9.1\mingw_64\bin\qmake.exe -install qinstall -exe
DEL_FILE      = del
SYMLINK       = $(QMAKE) -install ln -f -s
DEL_DIR       = rmdir
MOVE          = move
IDC           = idc
IDL           = midl
ZIP           = zip -r -9
DEF_FILE      = 
RES_FILE      = 
SED           = $(QMAKE) -install sed
MOVE          = move

####### Output directory

OBJECTS_DIR   = release

####### Files

SOURCES       = ..\main.cpp \
		..\modelmanager.cpp \
		..\monitorwindow.cpp \
		..\monitoring_datasource.cpp \
		..\datascreen.cpp \
		..\videoplayer.cpp \
		..\videomanager.cpp \
		..\boiler.cpp \
		..\config_manager.cpp \
		..\configmanager_qml.cpp \
		..\smoke_analyzer_comm.cpp \
		..\csvfile.cpp \
		..\csvreader.cpp \
		..\dcs.cpp release\qrc_qml.cpp \
		release\moc_modelmanager.cpp \
		release\moc_monitorwindow.cpp \
		release\moc_monitoring_datasource.cpp \
		release\moc_datascreen.cpp \
		release\moc_videoplayer.cpp \
		release\moc_videomanager.cpp \
		release\moc_configmanager_qml.cpp \
		release\moc_csvreader.cpp
OBJECTS       = release/main.o \
		release/modelmanager.o \
		release/monitorwindow.o \
		release/monitoring_datasource.o \
		release/datascreen.o \
		release/videoplayer.o \
		release/videomanager.o \
		release/boiler.o \
		release/config_manager.o \
		release/configmanager_qml.o \
		release/smoke_analyzer_comm.o \
		release/csvfile.o \
		release/csvreader.o \
		release/dcs.o \
		release/qrc_qml.o \
		release/moc_modelmanager.o \
		release/moc_monitorwindow.o \
		release/moc_monitoring_datasource.o \
		release/moc_datascreen.o \
		release/moc_videoplayer.o \
		release/moc_videomanager.o \
		release/moc_configmanager_qml.o \
		release/moc_csvreader.o

DIST          =  ..\modelmanager.h \
		..\monitorwindow.h \
		..\monitoring_datasource.h \
		..\datascreen.h \
		..\videoplayer.h \
		..\videomanager.h \
		..\boiler.h \
		..\config_manager.h \
		..\configmanager_qml.h \
		..\smoke_analyzer_comm.h \
		..\csvfile.h \
		..\csvreader.h \
		..\dcs.h ..\main.cpp \
		..\modelmanager.cpp \
		..\monitorwindow.cpp \
		..\monitoring_datasource.cpp \
		..\datascreen.cpp \
		..\videoplayer.cpp \
		..\videomanager.cpp \
		..\boiler.cpp \
		..\config_manager.cpp \
		..\configmanager_qml.cpp \
		..\smoke_analyzer_comm.cpp \
		..\csvfile.cpp \
		..\csvreader.cpp \
		..\dcs.cpp
QMAKE_TARGET  = SmartBurning
DESTDIR        = release\ #avoid trailing-slash linebreak
TARGET         = SmartBurning.exe
DESTDIR_TARGET = release\SmartBurning.exe

####### Build rules

first: all
all: Makefile.Release  release/SmartBurning.exe

release/SmartBurning.exe: D:/Development/Qt/6.9.1/mingw_64/lib/libQt6Charts.a D:/Development/Qt/6.9.1/mingw_64/lib/libQt6OpenGLWidgets.a D:/Development/Qt/6.9.1/mingw_64/lib/libQt6MultimediaWidgets.a D:/Development/Qt/6.9.1/mingw_64/lib/libQt6Widgets.a D:/Development/Qt/6.9.1/mingw_64/lib/libQt6QuickControls2.a D:/Development/Qt/6.9.1/mingw_64/lib/libQt6Quick.a D:/Development/Qt/6.9.1/mingw_64/lib/libQt6OpenGL.a D:/Development/Qt/6.9.1/mingw_64/lib/libQt6Multimedia.a D:/Development/Qt/6.9.1/mingw_64/lib/libQt6Gui.a D:/Development/Qt/6.9.1/mingw_64/lib/libQt6QmlMeta.a D:/Development/Qt/6.9.1/mingw_64/lib/libQt6QmlModels.a D:/Development/Qt/6.9.1/mingw_64/lib/libQt6QmlWorkerScript.a D:/Development/Qt/6.9.1/mingw_64/lib/libQt6Qml.a D:/Development/Qt/6.9.1/mingw_64/lib/libQt6Network.a D:/Development/Qt/6.9.1/mingw_64/lib/libQt6Core.a D:/Development/Qt/6.9.1/mingw_64/lib/libQt6EntryPoint.a ui_videoplayer.h $(OBJECTS) 
	$(LINKER) $(LFLAGS) -o $(DESTDIR_TARGET) @release\object_script.SmartBurning.Release $(LIBS)
	if not exist "E:\智慧燃烧\powerplant\build_64bit_release\release" mkdir "E:\智慧燃烧\powerplant\build_64bit_release\release" && if not exist "E:\智慧燃烧\powerplant\build_64bit_release\release\data" mkdir "E:\智慧燃烧\powerplant\build_64bit_release\release\data" && if not exist "E:\智慧燃烧\powerplant\build_64bit_release\release\video" mkdir "E:\智慧燃烧\powerplant\build_64bit_release\release\video" && copy /Y "E:\智慧燃烧\powerplant\config.ini" "E:\智慧燃烧\powerplant\build_64bit_release\release\" && if exist "E:\智慧燃烧\powerplant\video" xcopy /E /I /Y "E:\智慧燃烧\powerplant\video" "E:\智慧燃烧\powerplant\build_64bit_release\release\video"

qmake: FORCE
	@$(QMAKE) -o Makefile.Release ..\SmartBurning.pro -spec win32-g++ "CONFIG+=release" "CONFIG-=debug" "CONFIG-=qml_debug"

qmake_all: FORCE

dist:
	$(ZIP) SmartBurning.zip $(SOURCES) $(DIST) ..\..\SmartBurning.pro D:\Development\Qt\6.9.1\mingw_64\mkspecs\features\spec_pre.prf D:\Development\Qt\6.9.1\mingw_64\mkspecs\features\device_config.prf D:\Development\Qt\6.9.1\mingw_64\mkspecs\common\sanitize.conf D:\Development\Qt\6.9.1\mingw_64\mkspecs\common\gcc-base.conf D:\Development\Qt\6.9.1\mingw_64\mkspecs\common\g++-base.conf D:\Development\Qt\6.9.1\mingw_64\mkspecs\features\win32\windows_vulkan_sdk.prf D:\Development\Qt\6.9.1\mingw_64\mkspecs\common\windows-vulkan.conf D:\Development\Qt\6.9.1\mingw_64\mkspecs\common\g++-win32.conf D:\Development\Qt\6.9.1\mingw_64\mkspecs\common\windows-desktop.conf D:\Development\Qt\6.9.1\mingw_64\mkspecs\qconfig.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_ext_freetype.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_ext_libjpeg.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_ext_libpng.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_ext_openxr_loader.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_charts.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_charts_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_chartsqml.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_chartsqml_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_concurrent.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_concurrent_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_core.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_core_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_dbus.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_dbus_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_designer.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_designer_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_designercomponents_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_devicediscovery_support_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_entrypoint_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_example_icons_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_examples_asset_downloader_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_fb_support_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_ffmpegmediapluginimpl_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_freetype_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_gui.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_gui_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_harfbuzz_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_help.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_help_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_jpeg_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsanimation.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsanimation_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsfolderlistmodel.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsfolderlistmodel_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsplatform.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsplatform_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsqmlmodels.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labsqmlmodels_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labssettings.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labssettings_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labssharedimage.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labssharedimage_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labswavefrontmesh.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_labswavefrontmesh_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_linguist.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_multimedia.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_multimedia_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_multimediaquick_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_multimediatestlibprivate_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_multimediawidgets.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_multimediawidgets_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_network.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_network_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_opengl.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_opengl_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_openglwidgets.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_openglwidgets_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_packetprotocol_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_png_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_printsupport.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_printsupport_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qdoccatch_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qdoccatchconversions_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qdoccatchgenerators_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qml.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qml_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlassetdownloader.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlassetdownloader_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlcompiler.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlcompiler_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlcore.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlcore_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmldebug_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmldom_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlformat_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlintegration.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlintegration_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmllocalstorage.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmllocalstorage_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlls_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlmeta.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlmeta_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlmodels.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlmodels_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlnetwork.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlnetwork_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmltest.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmltest_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmltoolingsettings_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmltyperegistrar_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlworkerscript.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlworkerscript_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlxmllistmodel.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_qmlxmllistmodel_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3d.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3d_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dassetimport.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dassetimport_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dassetutils.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dassetutils_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3deffects.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3deffects_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dglslparser_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dhelpers.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dhelpers_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dhelpersimpl.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dhelpersimpl_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3diblbaker.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3diblbaker_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dparticleeffects.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dparticleeffects_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dparticles.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dparticles_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3druntimerender.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3druntimerender_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dspatialaudio_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dutils.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dutils_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dxr.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick3dxr_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quick_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basic.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basic_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2basicstyleimpl_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fluentwinui3styleimpl.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fluentwinui3styleimpl_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusion.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusion_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2fusionstyleimpl_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imagine.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imagine_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2imaginestyleimpl_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2impl.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2impl_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2material.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2material_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2materialstyleimpl_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universal.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universal_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2universalstyleimpl_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2windowsstyleimpl.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrols2windowsstyleimpl_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickcontrolstestutilsprivate_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2quickimpl_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2utils.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickdialogs2utils_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickeffects.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickeffects_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicklayouts.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicklayouts_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickparticles_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickshapes_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicktemplates2.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicktemplates2_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicktestutilsprivate_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicktimeline.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicktimeline_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicktimelineblendtrees.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quicktimelineblendtrees_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickvectorimage.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickvectorimage_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickvectorimagegenerator_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickwidgets.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_quickwidgets_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_shadertools.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_shadertools_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_spatialaudio.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_spatialaudio_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_sql.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_sql_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_svg.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_svg_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_svgwidgets.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_svgwidgets_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_testinternals_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_testlib.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_testlib_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_tools_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_uiplugin.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_uitools.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_uitools_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_widgets.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_widgets_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_xml.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_xml_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\modules\qt_lib_zlib_private.pri D:\Development\Qt\6.9.1\mingw_64\mkspecs\features\qt_functions.prf D:\Development\Qt\6.9.1\mingw_64\mkspecs\features\qt_config.prf D:\Development\Qt\6.9.1\mingw_64\mkspecs\win32-g++\qmake.conf D:\Development\Qt\6.9.1\mingw_64\mkspecs\features\spec_post.prf ..\.qmake.stash D:\Development\Qt\6.9.1\mingw_64\mkspecs\features\exclusive_builds.prf D:\Development\Qt\6.9.1\mingw_64\mkspecs\features\toolchain.prf D:\Development\Qt\6.9.1\mingw_64\mkspecs\features\default_pre.prf D:\Development\Qt\6.9.1\mingw_64\mkspecs\features\win32\default_pre.prf D:\Development\Qt\6.9.1\mingw_64\mkspecs\features\resolve_config.prf D:\Development\Qt\6.9.1\mingw_64\mkspecs\features\exclusive_builds_post.prf D:\Development\Qt\6.9.1\mingw_64\mkspecs\features\default_post.prf D:\Development\Qt\6.9.1\mingw_64\mkspecs\features\build_pass.prf D:\Development\Qt\6.9.1\mingw_64\mkspecs\features\precompile_header.prf D:\Development\Qt\6.9.1\mingw_64\mkspecs\features\warn_on.prf D:\Development\Qt\6.9.1\mingw_64\mkspecs\features\permissions.prf D:\Development\Qt\6.9.1\mingw_64\mkspecs\features\qt.prf D:\Development\Qt\6.9.1\mingw_64\mkspecs\features\resources_functions.prf D:\Development\Qt\6.9.1\mingw_64\mkspecs\features\resources.prf D:\Development\Qt\6.9.1\mingw_64\mkspecs\features\moc.prf D:\Development\Qt\6.9.1\mingw_64\mkspecs\features\win32\opengl.prf D:\Development\Qt\6.9.1\mingw_64\mkspecs\features\uic.prf D:\Development\Qt\6.9.1\mingw_64\mkspecs\features\qmake_use.prf D:\Development\Qt\6.9.1\mingw_64\mkspecs\features\file_copies.prf D:\Development\Qt\6.9.1\mingw_64\mkspecs\features\win32\windows.prf D:\Development\Qt\6.9.1\mingw_64\mkspecs\features\testcase_targets.prf D:\Development\Qt\6.9.1\mingw_64\mkspecs\features\exceptions.prf D:\Development\Qt\6.9.1\mingw_64\mkspecs\features\yacc.prf D:\Development\Qt\6.9.1\mingw_64\mkspecs\features\lex.prf ..\SmartBurning.pro ..\qml.qrc D:\Development\Qt\6.9.1\mingw_64\lib\Qt6Charts.prl D:\Development\Qt\6.9.1\mingw_64\lib\Qt6OpenGLWidgets.prl D:\Development\Qt\6.9.1\mingw_64\lib\Qt6MultimediaWidgets.prl D:\Development\Qt\6.9.1\mingw_64\lib\Qt6Widgets.prl D:\Development\Qt\6.9.1\mingw_64\lib\Qt6QuickControls2.prl D:\Development\Qt\6.9.1\mingw_64\lib\Qt6Quick.prl D:\Development\Qt\6.9.1\mingw_64\lib\Qt6OpenGL.prl D:\Development\Qt\6.9.1\mingw_64\lib\Qt6Multimedia.prl D:\Development\Qt\6.9.1\mingw_64\lib\Qt6Gui.prl D:\Development\Qt\6.9.1\mingw_64\lib\Qt6QmlMeta.prl D:\Development\Qt\6.9.1\mingw_64\lib\Qt6QmlModels.prl D:\Development\Qt\6.9.1\mingw_64\lib\Qt6QmlWorkerScript.prl D:\Development\Qt\6.9.1\mingw_64\lib\Qt6Qml.prl D:\Development\Qt\6.9.1\mingw_64\lib\Qt6Network.prl D:\Development\Qt\6.9.1\mingw_64\lib\Qt6Core.prl D:\Development\Qt\6.9.1\mingw_64\lib\Qt6EntryPoint.prl SmartBurning_zh_CN.ts SmartBurning_zh_CN.ts  ..\qml.qrc D:\Development\Qt\6.9.1\mingw_64\mkspecs\features\data\dummy.cpp ..\modelmanager.h ..\monitorwindow.h ..\monitoring_datasource.h ..\datascreen.h ..\videoplayer.h ..\videomanager.h ..\boiler.h ..\config_manager.h ..\configmanager_qml.h ..\smoke_analyzer_comm.h ..\csvfile.h ..\csvreader.h ..\dcs.h  ..\main.cpp ..\modelmanager.cpp ..\monitorwindow.cpp ..\monitoring_datasource.cpp ..\datascreen.cpp ..\videoplayer.cpp ..\videomanager.cpp ..\boiler.cpp ..\config_manager.cpp ..\configmanager_qml.cpp ..\smoke_analyzer_comm.cpp ..\csvfile.cpp ..\csvreader.cpp ..\dcs.cpp ..\videoplayer.ui    

clean: compiler_clean 
	-$(DEL_FILE) release\main.o release\modelmanager.o release\monitorwindow.o release\monitoring_datasource.o release\datascreen.o release\videoplayer.o release\videomanager.o release\boiler.o release\config_manager.o release\configmanager_qml.o release\smoke_analyzer_comm.o release\csvfile.o release\csvreader.o release\dcs.o release\qrc_qml.o release\moc_modelmanager.o release\moc_monitorwindow.o release\moc_monitoring_datasource.o release\moc_datascreen.o release\moc_videoplayer.o release\moc_videomanager.o release\moc_configmanager_qml.o release\moc_csvreader.o

distclean: clean 
	-$(DEL_FILE) $(DESTDIR_TARGET)
	-$(DEL_FILE) Makefile.Release

mocclean: compiler_moc_header_clean compiler_moc_objc_header_clean compiler_moc_source_clean

mocables: compiler_moc_header_make_all compiler_moc_objc_header_make_all compiler_moc_source_make_all

check: first

benchmark: first

compiler_no_pch_compiler_make_all:
compiler_no_pch_compiler_clean:
compiler_rcc_make_all: release/qrc_qml.cpp
compiler_rcc_clean:
	-$(DEL_FILE) release\qrc_qml.cpp
release/qrc_qml.cpp: ../qml.qrc \
		D:/Development/Qt/6.9.1/mingw_64/bin/rcc.exe
	D:\Development\Qt\6.9.1\mingw_64\bin\rcc.exe -name qml --no-zstd ..\qml.qrc -o release\qrc_qml.cpp

compiler_moc_predefs_make_all: release/moc_predefs.h
compiler_moc_predefs_clean:
	-$(DEL_FILE) release\moc_predefs.h
release/moc_predefs.h: D:/Development/Qt/6.9.1/mingw_64/mkspecs/features/data/dummy.cpp
	g++ -fno-keep-inline-dllexport -D_WIN32_WINNT=0x0601 -DWINVER=0x0601 -D_WIN32_WINNT=0x0601 -O2 -std=gnu++1z -Wall -Wextra -Wextra -dM -E -o release\moc_predefs.h D:\Development\Qt\6.9.1\mingw_64\mkspecs\features\data\dummy.cpp

compiler_moc_header_make_all: release/moc_modelmanager.cpp release/moc_monitorwindow.cpp release/moc_monitoring_datasource.cpp release/moc_datascreen.cpp release/moc_videoplayer.cpp release/moc_videomanager.cpp release/moc_configmanager_qml.cpp release/moc_csvreader.cpp
compiler_moc_header_clean:
	-$(DEL_FILE) release\moc_modelmanager.cpp release\moc_monitorwindow.cpp release\moc_monitoring_datasource.cpp release\moc_datascreen.cpp release\moc_videoplayer.cpp release\moc_videomanager.cpp release\moc_configmanager_qml.cpp release\moc_csvreader.cpp
release/moc_modelmanager.cpp: ../modelmanager.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QObject \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QStringList \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QTimer \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtimer.h \
		release/moc_predefs.h \
		D:/Development/Qt/6.9.1/mingw_64/bin/moc.exe
	D:\Development\Qt\6.9.1\mingw_64\bin\moc.exe $(DEFINES) --include E:/智慧燃烧/powerplant/build_64bit_release/release/moc_predefs.h -ID:/Development/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -IE:/智慧燃烧/powerplant -ID:/Development/Qt/6.9.1/mingw_64/include -ID:/Development/Qt/6.9.1/mingw_64/include/QtCharts -ID:/Development/Qt/6.9.1/mingw_64/include/QtOpenGLWidgets -ID:/Development/Qt/6.9.1/mingw_64/include/QtMultimediaWidgets -ID:/Development/Qt/6.9.1/mingw_64/include/QtWidgets -ID:/Development/Qt/6.9.1/mingw_64/include/QtQuickControls2 -ID:/Development/Qt/6.9.1/mingw_64/include/QtQuick -ID:/Development/Qt/6.9.1/mingw_64/include/QtOpenGL -ID:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia -ID:/Development/Qt/6.9.1/mingw_64/include/QtGui -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlMeta -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlModels -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlWorkerScript -ID:/Development/Qt/6.9.1/mingw_64/include/QtQml -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlIntegration -ID:/Development/Qt/6.9.1/mingw_64/include/QtNetwork -ID:/Development/Qt/6.9.1/mingw_64/include/QtCore -I. -ID:/Development/MinGW/mingw64/include/c++/15.1.0 -ID:/Development/MinGW/mingw64/include/c++/15.1.0/x86_64-w64-mingw32 -ID:/Development/MinGW/mingw64/include/c++/15.1.0/backward -ID:/Development/MinGW/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include -ID:/Development/MinGW/mingw64/include -ID:/Development/MinGW/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed -ID:/Development/MinGW/mingw64/x86_64-w64-mingw32/include ..\modelmanager.h -o release\moc_modelmanager.cpp

release/moc_monitorwindow.cpp: ../monitorwindow.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QObject \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QTimer \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtimer.h \
		../monitoring_datasource.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QVariantList \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvariantlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QList \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QVariant \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QDateTime \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QThread \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qthread.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QMutex \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmutex.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtsan_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QThreadPool \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qthreadpool.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qrunnable.h \
		release/moc_predefs.h \
		D:/Development/Qt/6.9.1/mingw_64/bin/moc.exe
	D:\Development\Qt\6.9.1\mingw_64\bin\moc.exe $(DEFINES) --include E:/智慧燃烧/powerplant/build_64bit_release/release/moc_predefs.h -ID:/Development/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -IE:/智慧燃烧/powerplant -ID:/Development/Qt/6.9.1/mingw_64/include -ID:/Development/Qt/6.9.1/mingw_64/include/QtCharts -ID:/Development/Qt/6.9.1/mingw_64/include/QtOpenGLWidgets -ID:/Development/Qt/6.9.1/mingw_64/include/QtMultimediaWidgets -ID:/Development/Qt/6.9.1/mingw_64/include/QtWidgets -ID:/Development/Qt/6.9.1/mingw_64/include/QtQuickControls2 -ID:/Development/Qt/6.9.1/mingw_64/include/QtQuick -ID:/Development/Qt/6.9.1/mingw_64/include/QtOpenGL -ID:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia -ID:/Development/Qt/6.9.1/mingw_64/include/QtGui -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlMeta -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlModels -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlWorkerScript -ID:/Development/Qt/6.9.1/mingw_64/include/QtQml -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlIntegration -ID:/Development/Qt/6.9.1/mingw_64/include/QtNetwork -ID:/Development/Qt/6.9.1/mingw_64/include/QtCore -I. -ID:/Development/MinGW/mingw64/include/c++/15.1.0 -ID:/Development/MinGW/mingw64/include/c++/15.1.0/x86_64-w64-mingw32 -ID:/Development/MinGW/mingw64/include/c++/15.1.0/backward -ID:/Development/MinGW/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include -ID:/Development/MinGW/mingw64/include -ID:/Development/MinGW/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed -ID:/Development/MinGW/mingw64/x86_64-w64-mingw32/include ..\monitorwindow.h -o release\moc_monitorwindow.cpp

release/moc_monitoring_datasource.cpp: ../monitoring_datasource.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QObject \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QTimer \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QVariantList \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvariantlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QList \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QVariant \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QDateTime \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QThread \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qthread.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QMutex \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmutex.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtsan_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QThreadPool \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qthreadpool.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qrunnable.h \
		release/moc_predefs.h \
		D:/Development/Qt/6.9.1/mingw_64/bin/moc.exe
	D:\Development\Qt\6.9.1\mingw_64\bin\moc.exe $(DEFINES) --include E:/智慧燃烧/powerplant/build_64bit_release/release/moc_predefs.h -ID:/Development/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -IE:/智慧燃烧/powerplant -ID:/Development/Qt/6.9.1/mingw_64/include -ID:/Development/Qt/6.9.1/mingw_64/include/QtCharts -ID:/Development/Qt/6.9.1/mingw_64/include/QtOpenGLWidgets -ID:/Development/Qt/6.9.1/mingw_64/include/QtMultimediaWidgets -ID:/Development/Qt/6.9.1/mingw_64/include/QtWidgets -ID:/Development/Qt/6.9.1/mingw_64/include/QtQuickControls2 -ID:/Development/Qt/6.9.1/mingw_64/include/QtQuick -ID:/Development/Qt/6.9.1/mingw_64/include/QtOpenGL -ID:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia -ID:/Development/Qt/6.9.1/mingw_64/include/QtGui -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlMeta -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlModels -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlWorkerScript -ID:/Development/Qt/6.9.1/mingw_64/include/QtQml -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlIntegration -ID:/Development/Qt/6.9.1/mingw_64/include/QtNetwork -ID:/Development/Qt/6.9.1/mingw_64/include/QtCore -I. -ID:/Development/MinGW/mingw64/include/c++/15.1.0 -ID:/Development/MinGW/mingw64/include/c++/15.1.0/x86_64-w64-mingw32 -ID:/Development/MinGW/mingw64/include/c++/15.1.0/backward -ID:/Development/MinGW/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include -ID:/Development/MinGW/mingw64/include -ID:/Development/MinGW/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed -ID:/Development/MinGW/mingw64/x86_64-w64-mingw32/include ..\monitoring_datasource.h -o release\moc_monitoring_datasource.cpp

release/moc_datascreen.cpp: ../datascreen.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QObject \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QString \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QTimer \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QStringList \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QDateTime \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		release/moc_predefs.h \
		D:/Development/Qt/6.9.1/mingw_64/bin/moc.exe
	D:\Development\Qt\6.9.1\mingw_64\bin\moc.exe $(DEFINES) --include E:/智慧燃烧/powerplant/build_64bit_release/release/moc_predefs.h -ID:/Development/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -IE:/智慧燃烧/powerplant -ID:/Development/Qt/6.9.1/mingw_64/include -ID:/Development/Qt/6.9.1/mingw_64/include/QtCharts -ID:/Development/Qt/6.9.1/mingw_64/include/QtOpenGLWidgets -ID:/Development/Qt/6.9.1/mingw_64/include/QtMultimediaWidgets -ID:/Development/Qt/6.9.1/mingw_64/include/QtWidgets -ID:/Development/Qt/6.9.1/mingw_64/include/QtQuickControls2 -ID:/Development/Qt/6.9.1/mingw_64/include/QtQuick -ID:/Development/Qt/6.9.1/mingw_64/include/QtOpenGL -ID:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia -ID:/Development/Qt/6.9.1/mingw_64/include/QtGui -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlMeta -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlModels -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlWorkerScript -ID:/Development/Qt/6.9.1/mingw_64/include/QtQml -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlIntegration -ID:/Development/Qt/6.9.1/mingw_64/include/QtNetwork -ID:/Development/Qt/6.9.1/mingw_64/include/QtCore -I. -ID:/Development/MinGW/mingw64/include/c++/15.1.0 -ID:/Development/MinGW/mingw64/include/c++/15.1.0/x86_64-w64-mingw32 -ID:/Development/MinGW/mingw64/include/c++/15.1.0/backward -ID:/Development/MinGW/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include -ID:/Development/MinGW/mingw64/include -ID:/Development/MinGW/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed -ID:/Development/MinGW/mingw64/x86_64-w64-mingw32/include ..\datascreen.h -o release\moc_datascreen.cpp

release/moc_videoplayer.cpp: ../videoplayer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/QWidget \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qwidget.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qtguiglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qtgui-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qtguiexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmargins.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qaction.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qkeysequence.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qicon.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsize.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpixmap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpaintdevice.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qrect.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qpoint.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qcolor.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qrgb.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qrgba64.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qimage.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpixelformat.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qtransform.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpolygon.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qregion.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qspan.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qline.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpalette.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qbrush.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qfont.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qendian.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qfontmetrics.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qfontinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qfontvariableaxis.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qcursor.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qbitmap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qevent.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qurl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qeventpoint.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qvector2d.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qvectornd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpointingdevice.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qinputdevice.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qscreen.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QList \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QObject \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QRect \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QSize \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QSizeF \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/QTransform \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnativeinterface.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qscreen_platform.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfuture.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfutureinterface.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmutex.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtsan_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qresultstore.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfuture_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qthreadpool.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qthread.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qrunnable.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qexception.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qpromise.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qinputmethod.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication_platform.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia/QMediaPlayer \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia/qmediaplayer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia/qtmultimediaglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia/qtmultimedia-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia/qtmultimediaexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia/qtaudio.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia/qaudio.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimediaWidgets/QVideoWidget \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimediaWidgets/qvideowidget.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimediaWidgets/qtmultimediawidgetsglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimediaWidgets/qtmultimediawidgetsexports.h \
		release/moc_predefs.h \
		D:/Development/Qt/6.9.1/mingw_64/bin/moc.exe
	D:\Development\Qt\6.9.1\mingw_64\bin\moc.exe $(DEFINES) --include E:/智慧燃烧/powerplant/build_64bit_release/release/moc_predefs.h -ID:/Development/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -IE:/智慧燃烧/powerplant -ID:/Development/Qt/6.9.1/mingw_64/include -ID:/Development/Qt/6.9.1/mingw_64/include/QtCharts -ID:/Development/Qt/6.9.1/mingw_64/include/QtOpenGLWidgets -ID:/Development/Qt/6.9.1/mingw_64/include/QtMultimediaWidgets -ID:/Development/Qt/6.9.1/mingw_64/include/QtWidgets -ID:/Development/Qt/6.9.1/mingw_64/include/QtQuickControls2 -ID:/Development/Qt/6.9.1/mingw_64/include/QtQuick -ID:/Development/Qt/6.9.1/mingw_64/include/QtOpenGL -ID:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia -ID:/Development/Qt/6.9.1/mingw_64/include/QtGui -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlMeta -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlModels -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlWorkerScript -ID:/Development/Qt/6.9.1/mingw_64/include/QtQml -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlIntegration -ID:/Development/Qt/6.9.1/mingw_64/include/QtNetwork -ID:/Development/Qt/6.9.1/mingw_64/include/QtCore -I. -ID:/Development/MinGW/mingw64/include/c++/15.1.0 -ID:/Development/MinGW/mingw64/include/c++/15.1.0/x86_64-w64-mingw32 -ID:/Development/MinGW/mingw64/include/c++/15.1.0/backward -ID:/Development/MinGW/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include -ID:/Development/MinGW/mingw64/include -ID:/Development/MinGW/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed -ID:/Development/MinGW/mingw64/x86_64-w64-mingw32/include ..\videoplayer.h -o release\moc_videoplayer.cpp

release/moc_videomanager.cpp: ../videomanager.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QObject \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/QWidget \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qwidget.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qtguiglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qtgui-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qtguiexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmargins.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qaction.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qkeysequence.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qicon.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsize.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpixmap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpaintdevice.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qrect.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qpoint.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qcolor.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qrgb.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qrgba64.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qimage.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpixelformat.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qtransform.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpolygon.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qregion.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qspan.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qline.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpalette.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qbrush.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qfont.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qendian.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qfontmetrics.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qfontinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qfontvariableaxis.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qcursor.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qbitmap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qevent.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qurl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qeventpoint.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qvector2d.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qvectornd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpointingdevice.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qinputdevice.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qscreen.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QList \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QRect \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QSize \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QSizeF \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/QTransform \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnativeinterface.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qscreen_platform.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfuture.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfutureinterface.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmutex.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtsan_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qresultstore.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfuture_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qthreadpool.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qthread.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qrunnable.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qexception.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qpromise.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qinputmethod.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication_platform.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/QQmlEngine \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qqmlengine.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qjsengine.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtimezone.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qjsvalue.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qtqmlglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qtqml-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtNetwork/qtnetworkglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtNetwork/qtnetwork-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtNetwork/qtnetworkexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qtqmlexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qjsmanagedvalue.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qjsprimitivevalue.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qjsnumbercoercion.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qqmldebug.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qqml.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qqmlprivate.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qqmllist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qqmlparserstatus.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qqmlpropertyvaluesource.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetaobject.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qversionnumber.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtyperevision.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qqmlregistration.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQmlIntegration/qqmlintegration.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qqmlerror.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qqmlabstracturlinterceptor.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia/QMediaPlayer \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia/qmediaplayer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia/qtmultimediaglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia/qtmultimedia-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia/qtmultimediaexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia/qtaudio.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia/qaudio.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimediaWidgets/QVideoWidget \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimediaWidgets/qvideowidget.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimediaWidgets/qtmultimediawidgetsglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimediaWidgets/qtmultimediawidgetsexports.h \
		release/moc_predefs.h \
		D:/Development/Qt/6.9.1/mingw_64/bin/moc.exe
	D:\Development\Qt\6.9.1\mingw_64\bin\moc.exe $(DEFINES) --include E:/智慧燃烧/powerplant/build_64bit_release/release/moc_predefs.h -ID:/Development/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -IE:/智慧燃烧/powerplant -ID:/Development/Qt/6.9.1/mingw_64/include -ID:/Development/Qt/6.9.1/mingw_64/include/QtCharts -ID:/Development/Qt/6.9.1/mingw_64/include/QtOpenGLWidgets -ID:/Development/Qt/6.9.1/mingw_64/include/QtMultimediaWidgets -ID:/Development/Qt/6.9.1/mingw_64/include/QtWidgets -ID:/Development/Qt/6.9.1/mingw_64/include/QtQuickControls2 -ID:/Development/Qt/6.9.1/mingw_64/include/QtQuick -ID:/Development/Qt/6.9.1/mingw_64/include/QtOpenGL -ID:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia -ID:/Development/Qt/6.9.1/mingw_64/include/QtGui -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlMeta -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlModels -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlWorkerScript -ID:/Development/Qt/6.9.1/mingw_64/include/QtQml -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlIntegration -ID:/Development/Qt/6.9.1/mingw_64/include/QtNetwork -ID:/Development/Qt/6.9.1/mingw_64/include/QtCore -I. -ID:/Development/MinGW/mingw64/include/c++/15.1.0 -ID:/Development/MinGW/mingw64/include/c++/15.1.0/x86_64-w64-mingw32 -ID:/Development/MinGW/mingw64/include/c++/15.1.0/backward -ID:/Development/MinGW/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include -ID:/Development/MinGW/mingw64/include -ID:/Development/MinGW/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed -ID:/Development/MinGW/mingw64/x86_64-w64-mingw32/include ..\videomanager.h -o release\moc_videomanager.cpp

release/moc_configmanager_qml.cpp: ../configmanager_qml.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QObject \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QVariantMap \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvariantmap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QMap \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QVariant \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QString \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QStringList \
		../config_manager.h \
		release/moc_predefs.h \
		D:/Development/Qt/6.9.1/mingw_64/bin/moc.exe
	D:\Development\Qt\6.9.1\mingw_64\bin\moc.exe $(DEFINES) --include E:/智慧燃烧/powerplant/build_64bit_release/release/moc_predefs.h -ID:/Development/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -IE:/智慧燃烧/powerplant -ID:/Development/Qt/6.9.1/mingw_64/include -ID:/Development/Qt/6.9.1/mingw_64/include/QtCharts -ID:/Development/Qt/6.9.1/mingw_64/include/QtOpenGLWidgets -ID:/Development/Qt/6.9.1/mingw_64/include/QtMultimediaWidgets -ID:/Development/Qt/6.9.1/mingw_64/include/QtWidgets -ID:/Development/Qt/6.9.1/mingw_64/include/QtQuickControls2 -ID:/Development/Qt/6.9.1/mingw_64/include/QtQuick -ID:/Development/Qt/6.9.1/mingw_64/include/QtOpenGL -ID:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia -ID:/Development/Qt/6.9.1/mingw_64/include/QtGui -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlMeta -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlModels -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlWorkerScript -ID:/Development/Qt/6.9.1/mingw_64/include/QtQml -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlIntegration -ID:/Development/Qt/6.9.1/mingw_64/include/QtNetwork -ID:/Development/Qt/6.9.1/mingw_64/include/QtCore -I. -ID:/Development/MinGW/mingw64/include/c++/15.1.0 -ID:/Development/MinGW/mingw64/include/c++/15.1.0/x86_64-w64-mingw32 -ID:/Development/MinGW/mingw64/include/c++/15.1.0/backward -ID:/Development/MinGW/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include -ID:/Development/MinGW/mingw64/include -ID:/Development/MinGW/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed -ID:/Development/MinGW/mingw64/x86_64-w64-mingw32/include ..\configmanager_qml.h -o release\moc_configmanager_qml.cpp

release/moc_csvreader.cpp: ../csvreader.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QObject \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QString \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QStringList \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QVariantList \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvariantlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QList \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QVariant \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QVariantMap \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvariantmap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QMap \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QDateTime \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		release/moc_predefs.h \
		D:/Development/Qt/6.9.1/mingw_64/bin/moc.exe
	D:\Development\Qt\6.9.1\mingw_64\bin\moc.exe $(DEFINES) --include E:/智慧燃烧/powerplant/build_64bit_release/release/moc_predefs.h -ID:/Development/Qt/6.9.1/mingw_64/mkspecs/win32-g++ -IE:/智慧燃烧/powerplant -ID:/Development/Qt/6.9.1/mingw_64/include -ID:/Development/Qt/6.9.1/mingw_64/include/QtCharts -ID:/Development/Qt/6.9.1/mingw_64/include/QtOpenGLWidgets -ID:/Development/Qt/6.9.1/mingw_64/include/QtMultimediaWidgets -ID:/Development/Qt/6.9.1/mingw_64/include/QtWidgets -ID:/Development/Qt/6.9.1/mingw_64/include/QtQuickControls2 -ID:/Development/Qt/6.9.1/mingw_64/include/QtQuick -ID:/Development/Qt/6.9.1/mingw_64/include/QtOpenGL -ID:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia -ID:/Development/Qt/6.9.1/mingw_64/include/QtGui -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlMeta -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlModels -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlWorkerScript -ID:/Development/Qt/6.9.1/mingw_64/include/QtQml -ID:/Development/Qt/6.9.1/mingw_64/include/QtQmlIntegration -ID:/Development/Qt/6.9.1/mingw_64/include/QtNetwork -ID:/Development/Qt/6.9.1/mingw_64/include/QtCore -I. -ID:/Development/MinGW/mingw64/include/c++/15.1.0 -ID:/Development/MinGW/mingw64/include/c++/15.1.0/x86_64-w64-mingw32 -ID:/Development/MinGW/mingw64/include/c++/15.1.0/backward -ID:/Development/MinGW/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include -ID:/Development/MinGW/mingw64/include -ID:/Development/MinGW/mingw64/lib/gcc/x86_64-w64-mingw32/15.1.0/include-fixed -ID:/Development/MinGW/mingw64/x86_64-w64-mingw32/include ..\csvreader.h -o release\moc_csvreader.cpp

compiler_moc_objc_header_make_all:
compiler_moc_objc_header_clean:
compiler_moc_source_make_all:
compiler_moc_source_clean:
compiler_uic_make_all: ui_videoplayer.h
compiler_uic_clean:
	-$(DEL_FILE) ui_videoplayer.h
ui_videoplayer.h: ../videoplayer.ui \
		D:/Development/Qt/6.9.1/mingw_64/bin/uic.exe
	D:\Development\Qt\6.9.1\mingw_64\bin\uic.exe ..\videoplayer.ui -o ui_videoplayer.h

compiler_yacc_decl_make_all:
compiler_yacc_decl_clean:
compiler_yacc_impl_make_all:
compiler_yacc_impl_clean:
compiler_lex_make_all:
compiler_lex_clean:
compiler_clean: compiler_rcc_clean compiler_moc_predefs_clean compiler_moc_header_clean compiler_uic_clean 



####### Compile

release/main.o: ../main.cpp ../modelmanager.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QObject \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QStringList \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QTimer \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtimer.h \
		../monitorwindow.h \
		../monitoring_datasource.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QVariantList \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvariantlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QList \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QVariant \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QDateTime \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QThread \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qthread.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QMutex \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmutex.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtsan_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QThreadPool \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qthreadpool.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qrunnable.h \
		../datascreen.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QString \
		../videomanager.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/QWidget \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qwidget.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qtguiglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qtgui-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qtguiexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmargins.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qaction.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qkeysequence.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qicon.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsize.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpixmap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpaintdevice.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qrect.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qpoint.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qcolor.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qrgb.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qrgba64.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qimage.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpixelformat.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qtransform.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpolygon.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qregion.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qspan.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qline.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpalette.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qbrush.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qfont.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qendian.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qfontmetrics.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qfontinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qfontvariableaxis.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qcursor.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qbitmap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qevent.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qurl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qeventpoint.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qvector2d.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qvectornd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpointingdevice.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qinputdevice.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qscreen.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QRect \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QSize \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QSizeF \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/QTransform \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnativeinterface.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qscreen_platform.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfuture.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfutureinterface.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qresultstore.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfuture_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qexception.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qpromise.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qinputmethod.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication_platform.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/QQmlEngine \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qqmlengine.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qjsengine.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtimezone.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qjsvalue.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qtqmlglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qtqml-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtNetwork/qtnetworkglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtNetwork/qtnetwork-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtNetwork/qtnetworkexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qtqmlexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qjsmanagedvalue.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qjsprimitivevalue.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qjsnumbercoercion.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qqmldebug.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qqml.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qqmlprivate.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qqmllist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qqmlparserstatus.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qqmlpropertyvaluesource.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetaobject.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qversionnumber.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtyperevision.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qqmlregistration.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQmlIntegration/qqmlintegration.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qqmlerror.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qqmlabstracturlinterceptor.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia/QMediaPlayer \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia/qmediaplayer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia/qtmultimediaglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia/qtmultimedia-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia/qtmultimediaexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia/qtaudio.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia/qaudio.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimediaWidgets/QVideoWidget \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimediaWidgets/qvideowidget.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimediaWidgets/qtmultimediawidgetsglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimediaWidgets/qtmultimediawidgetsexports.h \
		../csvreader.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QVariantMap \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvariantmap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QMap \
		../smoke_analyzer_comm.h \
		../config_manager.h \
		../boiler.h \
		../configmanager_qml.h \
		../dcs.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/QApplication \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qapplication.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QLocale \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QTranslator \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtranslator.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/QQmlApplicationEngine \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qqmlapplicationengine.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/QQmlContext \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qqmlcontext.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QDir \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdir.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdirlisting.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfiledevice.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfile.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfileinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QFile \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QCoreApplication
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\main.o ..\main.cpp

release/modelmanager.o: ../modelmanager.cpp ../modelmanager.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QObject \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QStringList \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QTimer \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QDebug \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\modelmanager.o ..\modelmanager.cpp

release/monitorwindow.o: ../monitorwindow.cpp ../monitorwindow.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QObject \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QTimer \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtimer.h \
		../monitoring_datasource.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QVariantList \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvariantlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QList \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QVariant \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QDateTime \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QThread \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qthread.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QMutex \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmutex.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtsan_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QThreadPool \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qthreadpool.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qrunnable.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\monitorwindow.o ..\monitorwindow.cpp

release/monitoring_datasource.o: ../monitoring_datasource.cpp ../monitoring_datasource.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QObject \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QTimer \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QVariantList \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvariantlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QList \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QVariant \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QDateTime \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QThread \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qthread.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QMutex \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmutex.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtsan_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QThreadPool \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qthreadpool.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qrunnable.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QVariantMap \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvariantmap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QMap \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QString \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCharts/QXYSeries \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCharts/qxyseries.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCharts/QChartGlobal \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCharts/qchartglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCharts/qtcharts-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCharts/qtchartsexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCharts/QAbstractSeries \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCharts/qabstractseries.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCharts/QAbstractAxis \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCharts/qabstractaxis.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/QPen \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpen.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qtguiglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qtgui-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qtguiexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qcolor.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qrgb.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qrgba64.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qbrush.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qpoint.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qimage.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpaintdevice.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qrect.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmargins.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsize.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpixelformat.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qtransform.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpolygon.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qregion.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qspan.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qline.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpixmap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/QFont \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qfont.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qendian.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/QBrush \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/QImage \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCharts/QLineSeries \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCharts/qlineseries.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QMutexLocker \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QRunnable \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QMetaObject \
		../smoke_analyzer_comm.h \
		../config_manager.h \
		../boiler.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\monitoring_datasource.o ..\monitoring_datasource.cpp

release/datascreen.o: ../datascreen.cpp ../datascreen.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QObject \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QString \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QTimer \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QStringList \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QDateTime \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		../smoke_analyzer_comm.h \
		../config_manager.h \
		../boiler.h \
		../dcs.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\datascreen.o ..\datascreen.cpp

release/videoplayer.o: ../videoplayer.cpp ../videoplayer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/QWidget \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qwidget.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qtguiglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qtgui-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qtguiexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmargins.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qaction.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qkeysequence.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qicon.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsize.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpixmap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpaintdevice.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qrect.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qpoint.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qcolor.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qrgb.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qrgba64.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qimage.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpixelformat.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qtransform.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpolygon.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qregion.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qspan.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qline.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpalette.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qbrush.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qfont.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qendian.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qfontmetrics.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qfontinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qfontvariableaxis.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qcursor.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qbitmap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qevent.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qurl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qeventpoint.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qvector2d.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qvectornd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpointingdevice.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qinputdevice.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qscreen.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QList \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QObject \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QRect \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QSize \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QSizeF \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/QTransform \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnativeinterface.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qscreen_platform.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfuture.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfutureinterface.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmutex.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtsan_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qresultstore.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfuture_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qthreadpool.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qthread.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qrunnable.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qexception.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qpromise.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qinputmethod.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication_platform.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia/QMediaPlayer \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia/qmediaplayer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia/qtmultimediaglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia/qtmultimedia-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia/qtmultimediaexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia/qtaudio.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia/qaudio.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimediaWidgets/QVideoWidget \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimediaWidgets/qvideowidget.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimediaWidgets/qtmultimediawidgetsglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimediaWidgets/qtmultimediawidgetsexports.h \
		../ui_videoplayer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QVariant \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/QApplication \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qapplication.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/QHBoxLayout \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qboxlayout.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qlayout.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qlayoutitem.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qgridlayout.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/QLabel \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qlabel.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qframe.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpicture.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qtextdocument.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/QPushButton \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qpushbutton.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractbutton.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/QSlider \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qslider.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qabstractslider.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/QVBoxLayout \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/QMainWindow \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qmainwindow.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qtabwidget.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/QFileDialog \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qfiledialog.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdir.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdirlisting.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfiledevice.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfile.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfileinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtimezone.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qdialog.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/QToolTip \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qtooltip.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\videoplayer.o ..\videoplayer.cpp

release/videomanager.o: ../videomanager.cpp ../videomanager.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QObject \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/QWidget \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qwidget.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qtguiglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qtgui-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qtguiexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgets-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qtwidgetsexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qwindowdefs_win.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmargins.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qaction.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qkeysequence.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qicon.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsize.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpixmap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpaintdevice.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qrect.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qpoint.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qcolor.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qrgb.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qrgba64.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qimage.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpixelformat.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qtransform.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpolygon.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qregion.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qspan.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qline.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpalette.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qbrush.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qfont.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qendian.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qfontmetrics.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qfontinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qfontvariableaxis.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtWidgets/qsizepolicy.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qcursor.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qbitmap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qevent.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qurl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qeventpoint.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qvector2d.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qvectornd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qpointingdevice.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qinputdevice.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qscreen.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QList \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QRect \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QSize \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QSizeF \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/QTransform \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnativeinterface.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qscreen_platform.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfuture.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfutureinterface.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmutex.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtsan_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qresultstore.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfuture_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qthreadpool.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qthread.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qrunnable.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qexception.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qpromise.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qinputmethod.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtGui/qguiapplication_platform.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/QQmlEngine \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qqmlengine.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qjsengine.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtimezone.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qjsvalue.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qtqmlglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qtqml-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtNetwork/qtnetworkglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtNetwork/qtnetwork-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtNetwork/qtnetworkexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qtqmlexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qjsmanagedvalue.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qjsprimitivevalue.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qjsnumbercoercion.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qqmldebug.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qqml.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qqmlprivate.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qqmllist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qqmlparserstatus.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qqmlpropertyvaluesource.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetaobject.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qversionnumber.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtyperevision.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qqmlregistration.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQmlIntegration/qqmlintegration.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qqmlerror.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtQml/qqmlabstracturlinterceptor.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia/QMediaPlayer \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia/qmediaplayer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia/qtmultimediaglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia/qtmultimedia-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia/qtmultimediaexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia/qtaudio.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimedia/qaudio.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimediaWidgets/QVideoWidget \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimediaWidgets/qvideowidget.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimediaWidgets/qtmultimediawidgetsglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtMultimediaWidgets/qtmultimediawidgetsexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QDebug \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QFileInfo \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfileinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfile.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfiledevice.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QUrl
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\videomanager.o ..\videomanager.cpp

release/boiler.o: ../boiler.cpp ../boiler.h \
		../config_manager.h \
		../csvfile.h \
		../smoke_analyzer_comm.h \
		../dcs.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\boiler.o ..\boiler.cpp

release/config_manager.o: ../config_manager.cpp ../config_manager.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\config_manager.o ..\config_manager.cpp

release/configmanager_qml.o: ../configmanager_qml.cpp ../configmanager_qml.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QObject \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QVariantMap \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvariantmap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QMap \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QVariant \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QString \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QStringList \
		../config_manager.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QDebug \
		../smoke_analyzer_comm.h \
		../boiler.h \
		../dcs.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\configmanager_qml.o ..\configmanager_qml.cpp

release/smoke_analyzer_comm.o: ../smoke_analyzer_comm.cpp ../csvfile.h \
		../smoke_analyzer_comm.h \
		../config_manager.h \
		../boiler.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\smoke_analyzer_comm.o ..\smoke_analyzer_comm.cpp

release/csvfile.o: ../csvfile.cpp ../csvfile.h \
		../smoke_analyzer_comm.h \
		../config_manager.h \
		../boiler.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\csvfile.o ..\csvfile.cpp

release/csvreader.o: ../csvreader.cpp ../csvreader.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QObject \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobject.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnamespace.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcoreglobal.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtversionchecks.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtconfiginclude.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qconfig.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcore-config.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtconfigmacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationdefinitions.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompilerdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qprocessordetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsystemdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtcoreexports.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtdeprecationmarkers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtclasshelpermacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtpreprocessorsupport.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qassert.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtnoop.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtypes.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtversion.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtypeinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainerfwd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsysinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlogging.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qflags.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompare_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbasicatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qatomic_cxx11.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qgenericatomic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qyieldcpu.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qconstructormacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdarwinhelpers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qexceptionhandling.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qforeach.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qttypetraits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfunctionpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qglobalstatic.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmalloc.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qminmax.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnumeric.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qoverload.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qswap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtenvironmentvariables.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtresource.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qttranslation.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qversiontagging.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcompare.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstdlibdetection.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcomparehelpers.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20type_traits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtmetamacros.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobjectdefs_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfunctionaltools_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstring.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qchar.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearray.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qrefcount.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydata.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qpair.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydatapointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qarraydataops.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainertools_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qxptype_traits.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20functional.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20memory.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q17memory.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearrayview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringfwd.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringliteral.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlatin1stringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qanystringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qutf8stringview.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringtokenizer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringbuilder.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringconverter_base.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qhashfunctions.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiterator.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbytearraylist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qalgorithms.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstringmatcher.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcoreevent.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbasictimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qabstracteventdispatcher.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qeventloop.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdeadlinetimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qelapsedtimer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetatype.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdatastream.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qscopedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiodevicebase.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfloat16.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmath.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtformat_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiterable.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmetacontainer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontainerinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtaggedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qscopeguard.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qobject_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qbindingstorage.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QString \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QStringList \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QVariantList \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvariantlist.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QList \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QVariant \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvariant.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdebug.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtextstream.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcontiguouscache.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qshareddata.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qsharedpointer_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qshareddata_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qset.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qhash.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvarlengtharray.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q23utility.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20utility.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QVariantMap \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qvariantmap.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QMap \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QDateTime \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdatetime.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcalendar.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qlocale.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QFile \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfile.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfiledevice.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qiodevice.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qspan.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/q20iterator.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QTextStream \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QDir \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdir.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qdirlisting.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfileinfo.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtimezone.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QDebug \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QStandardPaths \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qstandardpaths.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/QCoreApplication \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qnativeinterface.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qcoreapplication_platform.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfuture.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfutureinterface.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qmutex.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qtsan_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qresultstore.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qfuture_impl.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qthreadpool.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qthread.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qrunnable.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qexception.h \
		D:/Development/Qt/6.9.1/mingw_64/include/QtCore/qpromise.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\csvreader.o ..\csvreader.cpp

release/dcs.o: ../dcs.cpp ../dcs.h \
		../config_manager.h \
		../smoke_analyzer_comm.h \
		../boiler.h \
		../csvfile.h
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\dcs.o ..\dcs.cpp

release/qrc_qml.o: release/qrc_qml.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\qrc_qml.o release\qrc_qml.cpp

release/moc_modelmanager.o: release/moc_modelmanager.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\moc_modelmanager.o release\moc_modelmanager.cpp

release/moc_monitorwindow.o: release/moc_monitorwindow.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\moc_monitorwindow.o release\moc_monitorwindow.cpp

release/moc_monitoring_datasource.o: release/moc_monitoring_datasource.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\moc_monitoring_datasource.o release\moc_monitoring_datasource.cpp

release/moc_datascreen.o: release/moc_datascreen.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\moc_datascreen.o release\moc_datascreen.cpp

release/moc_videoplayer.o: release/moc_videoplayer.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\moc_videoplayer.o release\moc_videoplayer.cpp

release/moc_videomanager.o: release/moc_videomanager.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\moc_videomanager.o release\moc_videomanager.cpp

release/moc_configmanager_qml.o: release/moc_configmanager_qml.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\moc_configmanager_qml.o release\moc_configmanager_qml.cpp

release/moc_csvreader.o: release/moc_csvreader.cpp 
	$(CXX) -c $(CXXFLAGS) $(INCPATH) -o release\moc_csvreader.o release\moc_csvreader.cpp

####### Install

install_video: first FORCE
	@if not exist E:$(INSTALL_ROOT:@msyshack@%=%)\智慧燃烧\powerplant\build_64bit_release\release\video mkdir E:$(INSTALL_ROOT:@msyshack@%=%)\智慧燃烧\powerplant\build_64bit_release\release\video & if not exist E:$(INSTALL_ROOT:@msyshack@%=%)\智慧燃烧\powerplant\build_64bit_release\release\video exit 1
	$(QINSTALL) E:\智慧燃烧\powerplant\video\video.mp4 E:$(INSTALL_ROOT:@msyshack@%=%)\智慧燃烧\powerplant\build_64bit_release\release\video\video.mp4

uninstall_video: FORCE
	-$(DEL_FILE) E:$(INSTALL_ROOT:@msyshack@%=%)\智慧燃烧\powerplant\build_64bit_release\release\video\video.mp4
	-$(DEL_DIR) E:$(INSTALL_ROOT:@msyshack@%=%)\智慧燃烧\powerplant\build_64bit_release\release\video 


install_config: first FORCE
	@if not exist E:$(INSTALL_ROOT:@msyshack@%=%)\智慧燃烧\powerplant\build_64bit_release\release mkdir E:$(INSTALL_ROOT:@msyshack@%=%)\智慧燃烧\powerplant\build_64bit_release\release & if not exist E:$(INSTALL_ROOT:@msyshack@%=%)\智慧燃烧\powerplant\build_64bit_release\release exit 1
	$(QINSTALL) E:\智慧燃烧\powerplant\config.ini E:$(INSTALL_ROOT:@msyshack@%=%)\智慧燃烧\powerplant\build_64bit_release\release\config.ini

uninstall_config: FORCE
	-$(DEL_FILE) E:$(INSTALL_ROOT:@msyshack@%=%)\智慧燃烧\powerplant\build_64bit_release\release\config.ini
	-$(DEL_DIR) E:$(INSTALL_ROOT:@msyshack@%=%)\智慧燃烧\powerplant\build_64bit_release\release 


install: install_video install_config  FORCE

uninstall: uninstall_video uninstall_config  FORCE

FORCE:

.SUFFIXES:

