import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import QtQuick.Window 2.15
import QtCharts

Window {
    id: root
    width: 1920
    height: 1080
    visible: true
    title: "华润电力湖北有限公司锅炉智慧燃烧系统"

    // 连接模型管理器信号
    Connections {
        target: modelManager
        function onModelSwitchCompleted(success, message) {
            if (success) {
                stackView.push(completionPage, {message: message})
            } else {
                // 显示错误消息
                errorDialog.text = message
                errorDialog.open()
            }
        }
    }

    // 错误对话框
    Dialog {
        id: errorDialog
        property alias text: errorLabel.text

        anchors.centerIn: parent
        width: 300
        height: 150
        title: "错误"

        Label {
            id: errorLabel
            anchors.centerIn: parent
            wrapMode: Text.WordWrap
            width: parent.width - 40
        }

        standardButtons: Dialog.Ok
    }

    StackView {
        id: stackView
        anchors.fill: parent
        initialItem: homePage
    }

    // 首页
    Component {
        id: homePage
        HomePage {
            onNavigateToModelList: stackView.push(modelListPage)
            onNavigateToMonitoring: stackView.push(monitorPage)
            onNavigateToDataScreen: stackView.push(dataScreenPage)
            onNavigateToVideo: stackView.push(videoPage)
            onNavigateToParameterAdjustment: stackView.push(parameterAdjustmentPage)
        }
    }

    // 模型列表页面
    Component {
        id: modelListPage
        ModelManagement {
            onNavigateBack: stackView.pop()
            onNavigateToSelection: stackView.push(modelSelectionPage)
            onNavigateToPassword: function(targetModel) {
                stackView.push(passwordPage, {targetModel: targetModel})
            }
            onNavigateToSecondPassword: function(targetModel) {
                stackView.push(secondPasswordPage, {targetModel: targetModel})
            }
            onNavigateToConfirmation: function(targetModel) {
                stackView.push(confirmationPage, {targetModel: targetModel})
            }
            onNavigateToLoading: function(targetModel) {
                stackView.push(loadingPage, {targetModel: targetModel})
            }
        }
    }

    // 模型选择页面
    Component {
        id: modelSelectionPage
        ModelSelection {
            onNavigateBack: stackView.pop()
            onNavigateToPassword: function(targetModel) {
                stackView.push(passwordPage, {targetModel: targetModel})
            }
        }
    }

    // 密码输入页面
    Component {
        id: passwordPage
        PasswordInput {
            onNavigateBack: stackView.pop()
            onNavigateToSecondPassword: function(targetModel) {
                stackView.push(secondPasswordPage, {targetModel: targetModel})
            }
        }
    }

    // 第二人密码输入页面
    Component {
        id: secondPasswordPage
        SecondPasswordInput {
            onNavigateBack: stackView.pop()
            onNavigateToConfirmation: function(targetModel) {
                stackView.push(confirmationPage, {targetModel: targetModel})
            }
        }
    }

    // 最终确认页面
    Component {
        id: confirmationPage
        ModelConfirmation {
            onNavigateBack: stackView.pop()
            onNavigateToLoading: function(targetModel) {
                stackView.push(loadingPage, {targetModel: targetModel})
            }
        }
    }

    // 加载页面
    Component {
        id: loadingPage
        ModelLoading {}
    }

    // 完成页面
    Component {
        id: completionPage
        ModelCompletion {
            onNavigateToHome: stackView.pop(null)
        }
    }

    // 数据监控页面
    Component {
        id: monitorPage
        MonitoringSystem {
            onNavigateBack: {
                monitorWindow.stopMonitoring()
                stackView.pop()
            }
        }
    }

    // 采集配置页面
    Component {
        id: configPage
        CollectionConfigView {
            onNavigateBack: stackView.pop()
        }
    }



    // 多维智慧燃烧控制系统页面
    Component {
        id: dataScreenPage
        DataScreenView {}
    }

    // 视频监控页面
    Component {
        id: videoPage
        VideoView {}
    }

    // 参数调整页面
    Component {
        id: parameterAdjustmentPage
        ParameterAdjustmentView {}
    }


}
